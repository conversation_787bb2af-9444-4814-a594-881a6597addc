
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime
from TEST11 import fetch_constraint_data, build_constraint_table
def group_dates_by_day(df: pd.DataFrame) -> pd.DataFrame:
    """Groups rows by the date in the first column so all entries for the same day are together."""
    first_col = df.columns[0]
    df[first_col] = pd.to_datetime(df[first_col], errors='coerce').dt.date
    df = df.sort_values(by=first_col).reset_index(drop=True)
    return df
def sort_within_dates_by_total(df: pd.DataFrame) -> pd.DataFrame:
    """Sorts rows within each date group in descending order based on the 'Total' column."""
    first_col = df.columns[0]
    df = df.groupby(first_col, group_keys=False).apply(
        lambda x: x.sort_values(by="Total", ascending=False)
    ).reset_index(drop=True)
    return df
def save_table_to_csv(df: pd.DataFrame, publication_datetime: str):
    """Rounds the 'Total' column to 2 decimals and saves the DataFrame to a CSV file."""
    df["Total"] = df["Total"].round(2)
    safe_datetime = publication_datetime.replace(":", "-")
    file_name = f"ConstraintPredictionTable_{safe_datetime}.csv"
    df.to_csv(file_name, index=False)
    print(f"CSV saved as {file_name}")
def compute_total_colors(df: pd.DataFrame, base_rgb=(9,35,255)):
    """Calculates the heatmap color for the 'Total' column based on its value range."""
    min_val, max_val = df["Total"].min(), df["Total"].max()
    colors = []
    for val in df["Total"]:
        opacity = (val - min_val) / (max_val - min_val + 1e-6)
        colors.append(f"rgba({base_rgb[0]}, {base_rgb[1]}, {base_rgb[2]}, {opacity:.2f})")
    return colors
def compute_row_hour_colors(df: pd.DataFrame, max_opacity=0.6, base_rgb=(9,35,255)):
    """Computes heatmap colors for each row across hour columns (1–24) with min values transparent."""
    hour_cols = [col for col in df.columns if isinstance(col,int) and 1<=col<=24]
    row_colors = []
    for _, row in df.iterrows():
        min_row, max_row = row[hour_cols].min(), row[hour_cols].max()
        if max_row == min_row:
            colors = [f"rgba({base_rgb[0]}, {base_rgb[1]}, {base_rgb[2]}, 0)" for _ in hour_cols]
        else:
            colors = [
                f"rgba({base_rgb[0]}, {base_rgb[1]}, {base_rgb[2]}, {((row[col]-min_row)/(max_row-min_row))*max_opacity:.2f})"
                for col in hour_cols
            ]
        row_colors.append(colors)
    return row_colors, hour_cols
def plot_table(df: pd.DataFrame, publication_datetime: str):
    """Generates and displays a Plotly table with heatmaps for 'Total' and hourly columns."""
    base_rgb = (9,35,255)
    row_bg = ["#f9f9f9" if i % 2 == 0 else "white" for i in range(len(df))]
    total_colors = compute_total_colors(df, base_rgb)
    row_hour_colors, hour_cols = compute_row_hour_colors(df, max_opacity=0.6, base_rgb=base_rgb)
    fill_colors = []
    for col in df.columns:
        if col == "Total":
            fill_colors.append(total_colors)
        elif col in hour_cols:
            fill_colors.append([row_hour_colors[i][hour_cols.index(col)] for i in range(len(df))])
        else:
            fill_colors.append(row_bg)
    title_color = f"rgb({base_rgb[0]}, {base_rgb[1]}, {base_rgb[2]})"
    fig = go.Figure(
        data=[go.Table(
            header=dict(
                values=list(df.columns),
                fill_color="#d3d3d3",
                align="center",
                font=dict(color="black", size=13, family="Arial Black"),
                height=30
            ),
            cells=dict(
                values=[df[col] for col in df.columns],
                fill_color=fill_colors,
                align="center",
                font=dict(color="black", size=11, family="Arial"),
            )
        )]
    )
    fig.update_layout(
        title={
            'text': f"<span style='font-size:28px; color:{title_color};'><b>Constraint Prediction Table</b></span><br>"
                    f"<span style='font-size:14px; color:black;'>Forecast run on {publication_datetime}</span>",
            'x': 0.5,
            'xanchor': 'center'
        },
        margin=dict(l=5,r=5,t=100,b=5)
    )
    fig.show()

if __name__ == "__main__":
    # user inputs, ex "2025-08-14 7:00"
    pub_dt_input = input("Enter publication datetime (YYYY-MM-DD HH:MM): ").strip()
    try:
        dt = datetime.strptime(pub_dt_input, "%Y-%m-%d %H:%M")
        formatted_dt = dt.strftime("%Y-%m-%dT%H:%M:%S-05:00")
    except ValueError:
        print("Please enter datetime in format: YYYY-MM-DD HH:MM")
        exit()
    _, raw_data = fetch_constraint_data(formatted_dt)
    df = build_constraint_table(raw_data)
    df = group_dates_by_day(df)
    df = sort_within_dates_by_total(df)
    save_table_to_csv(df, formatted_dt)
    plot_table(df, formatted_dt)
 
 