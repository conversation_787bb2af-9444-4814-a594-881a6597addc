import requests
import pandas as pd
from datetime import datetime
import os

# --- Constants ---
API_URL = "https://venom.iuat.woodmac.com/api/v1/Forecast/ConstraintPrediction"
HEADERS = {
    "accept": "application/json",
    "Authorization": "37bb8490-66c2-11f0-aef1-cb0a1b939326"  # Replace with valid token if needed
}

# --- Helper to parse constraint key ---
def parse_constraint_key(constraint_key: str) -> dict:
    """
    Parses a constraint key string into structured parts.
    Example expected format:
    'ConstraintName|Contingency|FromStation-KV|ToStation-KV'
    """
    parts = constraint_key.split("|")
    result = {
        "Constraint": parts[0] if len(parts) > 0 else None,
        "Contingency": parts[1] if len(parts) > 1 else None,
        "FromStation": None,
        "FromKV": None,
        "ToStation": None,
        "ToKV": None,
    }

    # Handle From side
    if len(parts) > 2 and "-" in parts[2]:
        from_parts = parts[2].split("-")
        result["FromStation"] = from_parts[0]
        result["FromKV"] = from_parts[1] if len(from_parts) > 1 else None

    # Handle To side
    if len(parts) > 3 and "-" in parts[3]:
        to_parts = parts[3].split("-")
        result["ToStation"] = to_parts[0]
        result["ToKV"] = to_parts[1] if len(to_parts) > 1 else None

    return result

# --- Fetch data using full publication datetime ---
def fetch_constraint_data(publication_datetime: str):
    try:
        url = f"{API_URL}?publicationDateTime={publication_datetime}"
        print("Requesting:", url)

        auth = input("Please enter API key (enter 'n' to use default): ")

        if auth == 'n':
            response = requests.get(url, headers=HEADERS)
        else:
            HEADERS_new = {
                "accept": "application/json",
                "Authorization": auth
            }
            response = requests.get(url, headers=HEADERS_new)

        response.raise_for_status()
        data = response.json()
        print(f"Received JSON with keys: {list(data.keys())}")

        predictions = data.get("Predictions")
        if not isinstance(predictions, dict) or not predictions:
            print("'Predictions' not found or not a valid dictionary.")
            return publication_datetime, []

        rows = []
        for timestamp, values in predictions.items():
            try:
                dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                hour_ending = dt.hour + 1
                forecast_date = dt.date()
            except Exception as e:
                print(f"Failed to parse timestamp '{timestamp}': {e}")
                continue

            if not isinstance(values, dict):
                continue

            for key, shadow_price in values.items():
                if not isinstance(shadow_price, (int, float)):
                    continue
                rows.append({
                    "ConstraintKey": key,
                    "Hour": hour_ending,
                    "ShadowPrice": float(shadow_price),
                    "ForecastDate": str(forecast_date)
                })

        return publication_datetime, rows

    except Exception as e:
        print(f"Error fetching/parsing data: {e}")
        return publication_datetime, []

# --- Build summary table ---
def build_constraint_table(raw_rows):
    if not raw_rows:
        return pd.DataFrame()

    expanded_rows = []
    for row in raw_rows:
        parsed = parse_constraint_key(row["ConstraintKey"])
        parsed.update({
            "Hour": row["Hour"],
            "ShadowPrice": row["ShadowPrice"],
            "ForecastDate": row["ForecastDate"]
        })
        expanded_rows.append(parsed)

    df = pd.DataFrame(expanded_rows)
    if df.empty:
        return df

    pivot = df.pivot_table(
        index=["Constraint", "Contingency", "FromStation", "FromKV", "ToStation", "ToKV", "ForecastDate"],
        columns="Hour",
        values="ShadowPrice",
        fill_value=0.0,
        aggfunc='first'
    ).reset_index()

    hour_columns = sorted([col for col in pivot.columns if isinstance(col, int)])

    pivot["Total"] = pivot[hour_columns].sum(axis=1)

    ordered_cols = [
        "ForecastDate", "Constraint", "Contingency", "FromStation", "FromKV", "ToStation", "ToKV", "Total"
    ] + hour_columns

    return pivot[ordered_cols].sort_values(by="Total", ascending=False).reset_index(drop=True)

# --- Export to Excel ---
def export_to_excel(df: pd.DataFrame, publication_datetime: str):
    if df.empty:
        print(" Nothing to export.")
        return

    safe_datetime = publication_datetime.replace(":", "-")
    file_name = f"ConstraintSummary_PubDate_{safe_datetime}.xlsx"

    try:
        df.to_excel(file_name, index=False)
        print(f" Exported to {os.path.abspath(file_name)}")
    except Exception as e:
        print(f" Failed to write Excel file: {e}")

# --- Main function ---
def generate_summary(publication_datetime: str):
    print(f" Generating summary for publication datetime: {publication_datetime}")

    try:
        dt = datetime.strptime(publication_datetime, "%Y-%m-%d %H:%M")
        publication_datetime = dt.strftime("%Y-%m-%dT%H:%M:%S-05:00")
    except ValueError:
        print(" Please enter datetime in format: YYYY-MM-DD HH:MM (e.g. 2025-08-06 06:00)")
        return

    publication_datetime, raw_data = fetch_constraint_data(publication_datetime)

    if not raw_data:
        print(" No data retrieved.")
        return

    print(f" Retrieved {len(raw_data)} records across 24 hours.")
    summary_df = build_constraint_table(raw_data)
    print(f" Summary created: {len(summary_df)} unique constraints.")

    # export_to_excel(summary_df, publication_datetime)
    # (CSV export is handled in run code)
